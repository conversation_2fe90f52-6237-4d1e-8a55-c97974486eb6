spacy_pkuseg-1.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
spacy_pkuseg-1.0.0.dist-info/LICENSE,sha256=xkJS8FW767cwY09tQ8e5Nu5SxmRPjQEgz6kspG_JRJg,1120
spacy_pkuseg-1.0.0.dist-info/METADATA,sha256=miXZ5aRRrPHdf5LvBdW1r1LR80buvFWMW_IfEDsZyeA,13640
spacy_pkuseg-1.0.0.dist-info/RECORD,,
spacy_pkuseg-1.0.0.dist-info/WHEEL,sha256=n0FEMMLy9qHhhtyJmRgiYJmIbJZhb9lSgyWMxwuCQPQ,101
spacy_pkuseg-1.0.0.dist-info/top_level.txt,sha256=WQA5Iou8O76GtEnWG0I6AJxt9EoRrJAG-iRGUOodTwo,13
spacy_pkuseg/__init__.py,sha256=8QazsGJoSMtO4Mdqg_HWF9LbWSXZoHgPj6m-0m0MfMg,17491
spacy_pkuseg/__pycache__/__init__.cpython-310.pyc,,
spacy_pkuseg/__pycache__/config.cpython-310.pyc,,
spacy_pkuseg/__pycache__/data.cpython-310.pyc,,
spacy_pkuseg/__pycache__/download.cpython-310.pyc,,
spacy_pkuseg/__pycache__/gradient.cpython-310.pyc,,
spacy_pkuseg/__pycache__/model.cpython-310.pyc,,
spacy_pkuseg/__pycache__/optimizer.cpython-310.pyc,,
spacy_pkuseg/__pycache__/res_summarize.cpython-310.pyc,,
spacy_pkuseg/__pycache__/scorer.cpython-310.pyc,,
spacy_pkuseg/__pycache__/trainer.cpython-310.pyc,,
spacy_pkuseg/config.py,sha256=8UoJnDkeFcmInvCKkUbztLc9ywqG0oxy-GCcdFIUK9M,5878
spacy_pkuseg/data.py,sha256=fyfRNUrvDHdW6Caz-FXUw0MCaEhJsE-JhmvmR6jukcs,14832
spacy_pkuseg/dicts/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy_pkuseg/dicts/__pycache__/__init__.cpython-310.pyc,,
spacy_pkuseg/dicts/default.pkl,sha256=PPRQ2VcZ1RiD-kkpEwK5AyNyBk2GgFsTC7LqAiMZbGc,4525959
spacy_pkuseg/download.py,sha256=LZzmfl5UWElinCeVs2UzbVJ5-YveZ6bIqLMwLaFqZAA,3777
spacy_pkuseg/feature_extractor.cp310-win_amd64.pyd,sha256=d_lRPw6TEcWou8pnWCh-sF1aFSyWFjit5QjJhmwAQqU,212992
spacy_pkuseg/feature_extractor.cpp,sha256=RmwyJuIdUuOoRIMfk_1BhpqgMqUVIVT2r4PYdaOvaKQ,1215573
spacy_pkuseg/feature_extractor.pyx,sha256=AXn5POpXt1TkUrBZ15Cr75d7z3LL-yKnP0GeesqmMa0,22572
spacy_pkuseg/gradient.py,sha256=wYB5E5VSMidwoNqUk59VQh30pW-1Q1F5_bGdcmRIOFs,1413
spacy_pkuseg/inference.cp310-win_amd64.pyd,sha256=rZDR2KYdTHIbSzJ9nUkJDqLiT4cNehZBmDz6AdNYShA,218112
spacy_pkuseg/inference.cpp,sha256=C1XSGtGAS7-HYimFUa2fzYXcmEuLH53KVqXHU6b0X-c,1382494
spacy_pkuseg/inference.pyx,sha256=FVMsLA_LMZUEtuGfAFUe_Ct12_eVvhAHtnFbvUhivU8,6261
spacy_pkuseg/model.py,sha256=P5caFPweGxdQdkqCvJsuU0D2KMeCfR5BlUrVXsNdB-M,3675
spacy_pkuseg/models/tagIndex.txt,sha256=MV7h-cLLPg_NmSy4pSyDFzzoI1Dp9KLrfa53DBX8-pg,42
spacy_pkuseg/optimizer.py,sha256=7HGDdz775NBuCjcvbopAWPA0mmkZ4wx_5dRASMG1ceA,4123
spacy_pkuseg/postag/__init__.py,sha256=rp5R1ih33Xmmgx3wswqafU4IeJLD9V-oBnOD9qINSfU,1831
spacy_pkuseg/postag/__pycache__/__init__.cpython-310.pyc,,
spacy_pkuseg/postag/__pycache__/model.cpython-310.pyc,,
spacy_pkuseg/postag/feature_extractor.cp310-win_amd64.pyd,sha256=8T5sWNTN43M13SyYNVgrgTnxMTNL4GQvg-lQuwnkvPo,156672
spacy_pkuseg/postag/feature_extractor.cpp,sha256=-HiDHKhHms3RM_yYm1fktz9l3n4olJajq35hmnBK8EA,931973
spacy_pkuseg/postag/feature_extractor.pyx,sha256=0NkBAKrzgUvskwAPphAf0BvEHDvqgDXAi5wvWl2v5dc,11292
spacy_pkuseg/postag/model.py,sha256=MXNU71xyXRE022pzj-sEmg127ErdFFpy6km3RQfFmmw,2760
spacy_pkuseg/res_summarize.py,sha256=2VY3Y0ONO651UtG3fAj8qsp2kfEaghjO1g106gT9Icc,3854
spacy_pkuseg/scorer.py,sha256=VLx-ztNSzjawdpm2d_fH8EFxkH6wOQrb3J5YEm_raJw,2838
spacy_pkuseg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spacy_pkuseg/tests/__pycache__/__init__.cpython-310.pyc,,
spacy_pkuseg/tests/__pycache__/test_import.cpython-310.pyc,,
spacy_pkuseg/tests/test_import.py,sha256=r7pnfy8Yn3tpwtw0lMauuuDLVTDfFtDa7dnwft0F4ao,45
spacy_pkuseg/trainer.py,sha256=57kYzSrK5KnkRGhZV3InQxd6kfZlH8ZI9cp6kU4mH5c,14599
