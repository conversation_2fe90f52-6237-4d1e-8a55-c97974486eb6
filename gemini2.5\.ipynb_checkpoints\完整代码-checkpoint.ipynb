{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 期末作业：基于新闻文本的知识图谱构建与分析\n", "\n", "## 功能介绍\n", "1. **主题分类**: 使用TF-IDF和K-Means对新闻进行主题聚类。\n", "2. **精准实体识别**: 利用spaCy进行命名实体识别 (NER)，并进行归一化处理。\n", "3. **精确关系抽取**: 在句子级别进行实体共现关系抽取，并捕获上下文。\n", "4. **网络分析与可视化**: 使用NetworkX进行图分析，并用pyvis生成带上下文提示的交互式知识图谱。\n", "5. **关系上下文展示**: 在可交互图谱中，当鼠标悬停在一条边上时，会显示建立该关系的来源句子，增强可解释性。\n", "\n", "## 生成文件\n", "- `thematic_analysis_report.json`: 各主题的分析报告。\n", "- `knowledge_graph_theme_X.html`: 每个主题的可交互知识图谱 (带悬停提示)。\n", "- `top_keywords_per_theme.json`: 每个主题的关键词。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库和配置"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#!/usr/bin/env python\n", "# -*- coding: utf-8 -*-\n", "\n", "import pandas as pd\n", "import re\n", "import jieba\n", "import spacy\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.cluster import KMeans\n", "from collections import defaultdict\n", "import networkx as nx\n", "from pyvis.network import Network\n", "import json\n", "import os"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 全局配置参数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# --- 全局配置 ---\n", "DATA_FILE = \"作业数据.csv\"\n", "OUTPUT_DIR = \"analysis_results\"\n", "N_CLUSTERS = 5\n", "TOP_N_KEYWORDS = 15\n", "\n", "# 加载spaCy中文模型\n", "try:\n", "    NLP = spacy.load(\"zh_core_web_sm\")\n", "except OSError:\n", "    print(\"错误: spaCy中文模型 'zh_core_web_sm' 未找到。\")\n", "    print(\"请运行: python -m spacy download zh_core_web_sm\")\n", "    exit()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 数据加载与预处理函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_and_clean_data(filename):\n", "    \"\"\"\n", "    加载并清理数据\n", "    \n", "    Args:\n", "        filename (str): 数据文件名\n", "    \n", "    Returns:\n", "        pd.DataFrame: 清理后的数据框\n", "    \"\"\"\n", "    print(f\"1.1. 正在加载数据: {filename}\")\n", "    try:\n", "        try:\n", "            df = pd.read_csv(filename, encoding='utf-8')\n", "        except UnicodeDecodeError:\n", "            df = pd.read_csv(filename, encoding='gbk')\n", "        df['text'] = df['title'].fillna('') + ' ' + df['content'].fillna('')\n", "        df['text'] = df['text'].apply(lambda x: re.sub(r'\\s+', ' ', x).strip())\n", "        df.dropna(subset=['text'], inplace=True)\n", "        print(f\"数据加载成功，共 {len(df)} 条新闻。\")\n", "        return df\n", "    except FileNotFoundError:\n", "        print(f\"错误: 数据文件 {filename} 未找到。\")\n", "        return None\n", "    except Exception as e:\n", "        print(f\"加载数据时发生未知错误: {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 新闻主题分类函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def chinese_word_segmentation(text):\n", "    \"\"\"\n", "    中文分词\n", "    \n", "    Args:\n", "        text (str): 输入文本\n", "    \n", "    Returns:\n", "        str: 分词后的文本\n", "    \"\"\"\n", "    return \" \".join(jieba.cut(text))\n", "\n", "def classify_news_by_theme(df, n_clusters):\n", "    \"\"\"\n", "    使用K-Means对新闻进行主题聚类\n", "    \n", "    Args:\n", "        df (pd.DataFrame): 新闻数据框\n", "        n_clusters (int): 聚类数量\n", "    \n", "    Returns:\n", "        pd.DataFrame: 添加了主题标签的数据框\n", "    \"\"\"\n", "    print(f\"\\n2.1. 正在对 {len(df)} 条新闻进行主题聚类 (K={n_clusters})...\")\n", "    df['segmented_text'] = df['text'].apply(chinese_word_segmentation)\n", "    vectorizer = TfidfVectorizer(max_df=0.95, min_df=2, stop_words=['的', '在', '和', '是', '了', '也', '中', '国', '德国'])\n", "    tfidf_matrix = vectorizer.fit_transform(df['segmented_text'])\n", "    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)\n", "    kmeans.fit(tfidf_matrix)\n", "    df['theme'] = kmeans.labels_\n", "    print(\"2.2. 主题聚类完成。正在提取各主题关键词...\")\n", "    feature_names = vectorizer.get_feature_names_out()\n", "    top_keywords = {}\n", "    for i in range(n_clusters):\n", "        order_centroids = kmeans.cluster_centers_.argsort()[:, ::-1]\n", "        terms = [feature_names[ind] for ind in order_centroids[i, :TOP_N_KEYWORDS]]\n", "        top_keywords[f\"主题 {i}\"] = terms\n", "        print(f\"主题 {i}: {', '.join(terms)}\")\n", "    with open(os.path.join(OUTPUT_DIR, 'top_keywords_per_theme.json'), 'w', encoding='utf-8') as f:\n", "        json.dump(top_keywords, f, ensure_ascii=False, indent=4)\n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 实体识别与关系抽取函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def normalize_entity_text(text):\n", "    \"\"\"\n", "    实体文本归一化处理\n", "    \n", "    Args:\n", "        text (str): 原始实体文本\n", "    \n", "    Returns:\n", "        str: 归一化后的实体文本\n", "    \"\"\"\n", "    text = re.sub(r'(说|表示|指出|强调|认为|主席|总理|外长|部长)$', '', text)\n", "    if \"习近平\" in text: return \"习近平\"\n", "    if \"李克强\" in text: return \"李克强\"\n", "    if \"朔尔茨\" in text: return \"朔尔茨\"\n", "    if \"贝尔伯克\" in text: return \"贝尔伯克\"\n", "    return text\n", "\n", "def extract_entities_and_relations(text):\n", "    \"\"\"\n", "    使用spaCy从单篇新闻中提取实体和带上下文的关系。\n", "    关系是一个字典列表，每个字典包含实体对和来源句子。\n", "    \n", "    Args:\n", "        text (str): 新闻文本\n", "    \n", "    Returns:\n", "        tuple: (entities, relations) 实体字典和关系列表\n", "    \"\"\"\n", "    doc = NLP(text)\n", "    entities = defaultdict(set)\n", "    relations = []\n", "    \n", "    entity_map = {}\n", "    for ent in doc.ents:\n", "        if ent.label_ in ['PERSON', 'GPE', 'ORG'] and len(ent.text) > 1:\n", "            original_text = ent.text.strip()\n", "            normalized_text = normalize_entity_text(original_text)\n", "            if len(normalized_text) > 1:\n", "                entity_map[original_text] = normalized_text\n", "                entities[ent.label_].add(normalized_text)\n", "\n", "    for sent in doc.sents:\n", "        sent_entities = []\n", "        for ent in sent.ents:\n", "            original_text = ent.text.strip()\n", "            if original_text in entity_map:\n", "                sent_entities.append(entity_map[original_text])\n", "        \n", "        unique_sent_entities = list(set(sent_entities))\n", "        if len(unique_sent_entities) >= 2:\n", "            for i in range(len(unique_sent_entities)):\n", "                for j in range(i + 1, len(unique_sent_entities)):\n", "                    # 存储关系字典，包含实体对和句子上下文\n", "                    relations.append({\n", "                        \"edge\": tuple(sorted((unique_sent_entities[i], unique_sent_entities[j]))),\n", "                        \"sentence\": sent.text.strip()\n", "                    })\n", "                    \n", "    return entities, relations"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 图构建、分析与可视化函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def build_and_analyze_graph(relations, theme_name):\n", "    \"\"\"\n", "    根据关系列表构建、分析并可视化知识图谱，边上带有悬停提示。\n", "    \n", "    Args:\n", "        relations (list): 关系列表\n", "        theme_name (str): 主题名称\n", "    \n", "    Returns:\n", "        dict: 图分析统计结果\n", "    \"\"\"\n", "    print(f\"\\n4.1. 正在为 '{theme_name}' 构建知识图谱...\")\n", "    \n", "    # 聚合关系，统计权重和来源句子\n", "    edge_data = defaultdict(lambda: {'weight': 0, 'sentences': []})\n", "    for rel in relations:\n", "        edge = rel['edge']\n", "        edge_data[edge]['weight'] += 1\n", "        # 只添加不重复的句子以避免提示信息过长\n", "        if rel['sentence'] not in edge_data[edge]['sentences']:\n", "            edge_data[edge]['sentences'].append(rel['sentence'])\n", "\n", "    G = nx.Graph()\n", "    for edge, data in edge_data.items():\n", "        u, v = edge\n", "        if u != v:\n", "            G.add_edge(u, v, weight=data['weight'], sentences=data['sentences'])\n", "            \n", "    if not G.nodes:\n", "        print(f\"'{theme_name}' 没有发现足够的关系来构建图。\")\n", "        return None\n", "\n", "    print(f\"图构建完成: {G.number_of_nodes()} 个节点, {G.number_of_edges()} 条边。\")\n", "    \n", "    degrees = G.degree()\n", "    node_degrees = {node: val for node, val in degrees}\n", "    top_10_nodes = sorted(node_degrees.items(), key=lambda x: x[1], reverse=True)[:10]\n", "\n", "    print(\"度数中心性排名前10的节点:\")\n", "    for node, degree in top_10_nodes:\n", "        print(f\"  - {node}: {degree}\")\n", "        \n", "    net = Network(height=\"800px\", width=\"100%\", bgcolor=\"#222222\", font_color=\"white\", notebook=False, directed=False)\n", "    \n", "    for node, degree in node_degrees.items():\n", "        net.add_node(node, label=node, value=degree * 3, title=f\"实体: {node}<br>度数: {degree}\")\n", "\n", "    for u, v, data in G.edges(data=True):\n", "        weight = data['weight']\n", "        sentences = data['sentences']\n", "        \n", "        # 创建悬停提示文本 (Tooltip)\n", "        hover_title = f\"<b>关系: {u} - {v}</b><br>\"\n", "        hover_title += f\"<b>共现次数: {weight}</b><br><hr>\"\n", "        hover_title += \"<b>来源句子示例:</b><br>\"\n", "        # 最多显示5条来源句子\n", "        for i, sent in enumerate(sentences[:5]):\n", "            hover_title += f\"  - {sent}<br>\"\n", "        if len(sentences) > 5:\n", "            hover_title += f\"<i>(...等共 {len(sentences)} 条)</i>\"\n", "            \n", "        net.add_edge(u, v, value=weight, title=hover_title, color='#87CEEB')\n", "        \n", "    net.repulsion(node_distance=150, spring_length=200)\n", "    net.show_buttons(filter_=['physics'])\n", "    \n", "    output_filename = os.path.join(OUTPUT_DIR, f\"knowledge_graph_{theme_name.replace(' ', '_')}.html\")\n", "    net.save_graph(output_filename)\n", "    print(f\"已保存可交互图谱: {output_filename}\")\n", "    \n", "    return {\n", "        \"nodes_count\": <PERSON>.number_of_nodes(),\n", "        \"edges_count\": G.number_of_edges(),\n", "        \"top_10_central_nodes\": top_10_nodes\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 主执行函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def main():\n", "    \"\"\"\n", "    主执行函数，完成整个知识图谱构建与分析流程\n", "    \"\"\"\n", "    print(\"=== 新闻知识图谱构建与分析 ===\")\n", "    \n", "    if not os.path.exists(OUTPUT_DIR):\n", "        os.makedirs(OUTPUT_DIR)\n", "        \n", "    df = load_and_clean_data(DATA_FILE)\n", "    if df is None: return\n", "\n", "    df_themed = classify_news_by_theme(df, n_clusters=N_CLUSTERS)\n", "    \n", "    thematic_reports = {}\n", "    \n", "    for theme_id in range(N_CLUSTERS):\n", "        theme_name = f\"主题 {theme_id}\"\n", "        print(f\"\\n{'='*20} 正在分析 {theme_name} {'='*20}\")\n", "        theme_df = df_themed[df_themed['theme'] == theme_id]\n", "        \n", "        all_theme_entities = defaultdict(set)\n", "        all_theme_relations = []\n", "        for text in theme_df['text']:\n", "            entities, relations = extract_entities_and_relations(text)\n", "            for label, ents in entities.items():\n", "                all_theme_entities[label].update(ents)\n", "            all_theme_relations.extend(relations)\n", "        \n", "        print(f\"在 '{theme_name}' 中共识别出 {sum(len(s) for s in all_theme_entities.values())} 个独立实体。\")\n", "        print(f\"共抽取到 {len(all_theme_relations)} 条关系记录。\")\n", "        \n", "        graph_stats = build_and_analyze_graph(all_theme_relations, theme_name)\n", "        if graph_stats:\n", "            thematic_reports[theme_name] = {\n", "                \"news_count\": len(theme_df),\n", "                \"entity_counts\": {label: len(ents) for label, ents in all_theme_entities.items()},\n", "                \"relation_record_count\": len(all_theme_relations),\n", "                \"graph_analysis\": graph_stats\n", "            }\n", "\n", "    \n", "    report_filename = os.path.join(OUTPUT_DIR, 'thematic_analysis_report.json')\n", "    with open(report_filename, 'w', encoding='utf-8') as f:\n", "        json.dump(thematic_reports, f, ensure_ascii=False, indent=4)\n", "    print(f\"\\n已保存完整分析报告: {report_filename}\")\n", "\n", "    print(\"\\n=== 分析流程全部完成 ===\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 执行主程序\n", "\n", "运行以下代码开始知识图谱构建与分析："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 执行主程序\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. 可选：单独运行各个步骤\n", "\n", "如果需要单独测试某个功能，可以运行以下代码块："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 示例：单独加载数据\n", "# df = load_and_clean_data(DATA_FILE)\n", "# print(f\"数据形状: {df.shape}\")\n", "# print(df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 示例：单独进行主题分类\n", "# df_themed = classify_news_by_theme(df, n_clusters=N_CLUSTERS)\n", "# print(df_themed['theme'].value_counts())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 示例：单独测试实体识别\n", "# sample_text = \"习近平主席与德国总理朔尔茨在北京举行会谈。\"\n", "# entities, relations = extract_entities_and_relations(sample_text)\n", "# print(f\"实体: {entities}\")\n", "# print(f\"关系: {relations}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.7"}}, "nbformat": 4, "nbformat_minor": 4}