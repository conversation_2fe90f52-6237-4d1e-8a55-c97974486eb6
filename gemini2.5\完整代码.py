#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
期末作业：基于新闻文本的知识图谱构建与分析

功能:
1.  主题分类: 使用TF-IDF和K-Means对新闻进行主题聚类。
2.  精准实体识别: 利用spaCy进行命名实体识别 (NER)，并进行归一化处理。
3.  精确关系抽取: 在句子级别进行实体共现关系抽取，并捕获上下文。
4.  网络分析与可视化: 使用NetworkX进行图分析，并用pyvis生成带上下文提示的交互式知识图谱。
5.  关系上下文展示: 在可交互图谱中，当鼠标悬停在一条边上时，会显示建立该关系的来源句子，增强可解释性。

生成文件:
-   thematic_analysis_report.json: 各主题的分析报告。
-   knowledge_graph_theme_X.html: 每个主题的可交互知识图谱 (带悬停提示)。
-   top_keywords_per_theme.json: 每个主题的关键词。
"""

import pandas as pd
import re
import jieba
import spacy
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
from collections import defaultdict
import networkx as nx
from pyvis.network import Network
import json
import os

# --- 全局配置 ---
DATA_FILE = "作业数据.csv"
OUTPUT_DIR = "analysis_results"
N_CLUSTERS = 5
TOP_N_KEYWORDS = 15

# 加载spaCy中文模型
try:
    NLP = spacy.load("zh_core_web_sm")
except OSError:
    print("错误: spaCy中文模型 'zh_core_web_sm' 未找到。")
    print("请运行: python -m spacy download zh_core_web_sm")
    exit()

# --- 步骤1: 数据加载与预处理  ---
def load_and_clean_data(filename):
    print(f"1.1. 正在加载数据: {filename}")
    try:
        try:
            df = pd.read_csv(filename, encoding='utf-8')
        except UnicodeDecodeError:
            df = pd.read_csv(filename, encoding='gbk')
        df['text'] = df['title'].fillna('') + ' ' + df['content'].fillna('')
        df['text'] = df['text'].apply(lambda x: re.sub(r'\s+', ' ', x).strip())
        df.dropna(subset=['text'], inplace=True)
        print(f"数据加载成功，共 {len(df)} 条新闻。")
        return df
    except FileNotFoundError:
        print(f"错误: 数据文件 {filename} 未找到。")
        return None
    except Exception as e:
        print(f"加载数据时发生未知错误: {e}")
        return None

# --- 步骤2: 新闻主题分类 ---
def chinese_word_segmentation(text):
    return " ".join(jieba.cut(text))

def classify_news_by_theme(df, n_clusters):
    print(f"\n2.1. 正在对 {len(df)} 条新闻进行主题聚类 (K={n_clusters})...")
    df['segmented_text'] = df['text'].apply(chinese_word_segmentation)
    vectorizer = TfidfVectorizer(max_df=0.95, min_df=2, stop_words=['的', '在', '和', '是', '了', '也', '中', '国', '德国'])
    tfidf_matrix = vectorizer.fit_transform(df['segmented_text'])
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
    kmeans.fit(tfidf_matrix)
    df['theme'] = kmeans.labels_
    print("2.2. 主题聚类完成。正在提取各主题关键词...")
    feature_names = vectorizer.get_feature_names_out()
    top_keywords = {}
    for i in range(n_clusters):
        order_centroids = kmeans.cluster_centers_.argsort()[:, ::-1]
        terms = [feature_names[ind] for ind in order_centroids[i, :TOP_N_KEYWORDS]]
        top_keywords[f"主题 {i}"] = terms
        print(f"主题 {i}: {', '.join(terms)}")
    with open(os.path.join(OUTPUT_DIR, 'top_keywords_per_theme.json'), 'w', encoding='utf-8') as f:
        json.dump(top_keywords, f, ensure_ascii=False, indent=4)
    return df

# --- 步骤3: 实体识别与关系抽取  ---
def normalize_entity_text(text):
    text = re.sub(r'(说|表示|指出|强调|认为|主席|总理|外长|部长)$', '', text)
    if "习近平" in text: return "习近平"
    if "李克强" in text: return "李克强"
    if "朔尔茨" in text: return "朔尔茨"
    if "贝尔伯克" in text: return "贝尔伯克"
    return text

def extract_entities_and_relations(text):
    """
    使用spaCy从单篇新闻中提取实体和带上下文的关系。
    关系是一个字典列表，每个字典包含实体对和来源句子。
    """
    doc = NLP(text)
    entities = defaultdict(set)
    relations = []
    
    entity_map = {}
    for ent in doc.ents:
        if ent.label_ in ['PERSON', 'GPE', 'ORG'] and len(ent.text) > 1:
            original_text = ent.text.strip()
            normalized_text = normalize_entity_text(original_text)
            if len(normalized_text) > 1:
                entity_map[original_text] = normalized_text
                entities[ent.label_].add(normalized_text)

    for sent in doc.sents:
        sent_entities = []
        for ent in sent.ents:
            original_text = ent.text.strip()
            if original_text in entity_map:
                sent_entities.append(entity_map[original_text])
        
        unique_sent_entities = list(set(sent_entities))
        if len(unique_sent_entities) >= 2:
            for i in range(len(unique_sent_entities)):
                for j in range(i + 1, len(unique_sent_entities)):
                    # 存储关系字典，包含实体对和句子上下文
                    relations.append({
                        "edge": tuple(sorted((unique_sent_entities[i], unique_sent_entities[j]))),
                        "sentence": sent.text.strip()
                    })
                    
    return entities, relations

# --- 步骤4: 图构建、分析与可视化 ---
def build_and_analyze_graph(relations, theme_name):
    """根据关系列表构建、分析并可视化知识图谱，边上带有悬停提示。"""
    print(f"\n4.1. 正在为 '{theme_name}' 构建知识图谱...")
    
    # 聚合关系，统计权重和来源句子
    edge_data = defaultdict(lambda: {'weight': 0, 'sentences': []})
    for rel in relations:
        edge = rel['edge']
        edge_data[edge]['weight'] += 1
        # 只添加不重复的句子以避免提示信息过长
        if rel['sentence'] not in edge_data[edge]['sentences']:
            edge_data[edge]['sentences'].append(rel['sentence'])

    G = nx.Graph()
    for edge, data in edge_data.items():
        u, v = edge
        if u != v:
            G.add_edge(u, v, weight=data['weight'], sentences=data['sentences'])
            
    if not G.nodes:
        print(f"'{theme_name}' 没有发现足够的关系来构建图。")
        return None

    print(f"图构建完成: {G.number_of_nodes()} 个节点, {G.number_of_edges()} 条边。")
    
    degrees = G.degree()
    node_degrees = {node: val for node, val in degrees}
    top_10_nodes = sorted(node_degrees.items(), key=lambda x: x[1], reverse=True)[:10]

    print("度数中心性排名前10的节点:")
    for node, degree in top_10_nodes:
        print(f"  - {node}: {degree}")
        
    net = Network(height="800px", width="100%", bgcolor="#222222", font_color="white", notebook=False, directed=False)
    
    for node, degree in node_degrees.items():
        net.add_node(node, label=node, value=degree * 3, title=f"实体: {node}<br>度数: {degree}")

    for u, v, data in G.edges(data=True):
        weight = data['weight']
        sentences = data['sentences']
        
        # 创建悬停提示文本 (Tooltip)
        hover_title = f"<b>关系: {u} - {v}</b><br>"
        hover_title += f"<b>共现次数: {weight}</b><br><hr>"
        hover_title += "<b>来源句子示例:</b><br>"
        # 最多显示5条来源句子
        for i, sent in enumerate(sentences[:5]):
            hover_title += f"  - {sent}<br>"
        if len(sentences) > 5:
            hover_title += f"<i>(...等共 {len(sentences)} 条)</i>"
            
        net.add_edge(u, v, value=weight, title=hover_title, color='#87CEEB')
        
    net.repulsion(node_distance=150, spring_length=200)
    net.show_buttons(filter_=['physics'])
    
    output_filename = os.path.join(OUTPUT_DIR, f"knowledge_graph_{theme_name.replace(' ', '_')}.html")
    net.save_graph(output_filename)
    print(f"已保存可交互图谱: {output_filename}")
    
    return {
        "nodes_count": G.number_of_nodes(),
        "edges_count": G.number_of_edges(),
        "top_10_central_nodes": top_10_nodes
    }

# --- 主执行函数  ---
def main():
    print("=== 新闻知识图谱构建与分析 ===")
    
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        
    df = load_and_clean_data(DATA_FILE)
    if df is None: return

    df_themed = classify_news_by_theme(df, n_clusters=N_CLUSTERS)
    
    thematic_reports = {}
    
    for theme_id in range(N_CLUSTERS):
        theme_name = f"主题 {theme_id}"
        print(f"\n{'='*20} 正在分析 {theme_name} {'='*20}")
        theme_df = df_themed[df_themed['theme'] == theme_id]
        
        all_theme_entities = defaultdict(set)
        all_theme_relations = []
        for text in theme_df['text']:
            entities, relations = extract_entities_and_relations(text)
            for label, ents in entities.items():
                all_theme_entities[label].update(ents)
            all_theme_relations.extend(relations)
        
        print(f"在 '{theme_name}' 中共识别出 {sum(len(s) for s in all_theme_entities.values())} 个独立实体。")
        print(f"共抽取到 {len(all_theme_relations)} 条关系记录。")
        
        graph_stats = build_and_analyze_graph(all_theme_relations, theme_name)
        if graph_stats:
            thematic_reports[theme_name] = {
                "news_count": len(theme_df),
                "entity_counts": {label: len(ents) for label, ents in all_theme_entities.items()},
                "relation_record_count": len(all_theme_relations),
                "graph_analysis": graph_stats
            }


    
    report_filename = os.path.join(OUTPUT_DIR, 'thematic_analysis_report.json')
    with open(report_filename, 'w', encoding='utf-8') as f:
        json.dump(thematic_reports, f, ensure_ascii=False, indent=4)
    print(f"\n已保存完整分析报告: {report_filename}")

    print("\n=== 分析流程全部完成 ===")

if __name__ == "__main__":
    main()