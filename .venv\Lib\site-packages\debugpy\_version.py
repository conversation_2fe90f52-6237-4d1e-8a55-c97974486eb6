
# This file was generated by 'versioneer.py' (0.23) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-04-10T11:38:49-0700",
 "dirty": false,
 "error": null,
 "full-revisionid": "8b5b84aec3d4ca6fcc5d17edff5627ba19f7d9e6",
 "version": "1.8.14"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
