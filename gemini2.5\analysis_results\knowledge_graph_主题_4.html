<html>
    <head>
        <meta charset="utf-8">
        
            <script src="lib/bindings/utils.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 800px;
                 background-color: #222222;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             

             
             #config {
                 float: left;
                 width: 400px;
                 height: 600px;
             }
             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
        
            <div id="config"></div>
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#97c2fc", "font": {"color": "white"}, "id": "\u5916\u4ea4\u90e8", "label": "\u5916\u4ea4\u90e8", "shape": "dot", "title": "\u5b9e\u4f53: \u5916\u4ea4\u90e8\u003cbr\u003e\u5ea6\u6570: 4", "value": 12}, {"color": "#97c2fc", "font": {"color": "white"}, "id": "\u5b89\u5357", "label": "\u5b89\u5357", "shape": "dot", "title": "\u5b9e\u4f53: \u5b89\u5357\u003cbr\u003e\u5ea6\u6570: 5", "value": 15}, {"color": "#97c2fc", "font": {"color": "white"}, "id": "\u4e2d\u5fb7", "label": "\u4e2d\u5fb7", "shape": "dot", "title": "\u5b9e\u4f53: \u4e2d\u5fb7\u003cbr\u003e\u5ea6\u6570: 4", "value": 12}, {"color": "#97c2fc", "font": {"color": "white"}, "id": "\u5fb7\u56fd\u5916\u4ea4\u90e8", "label": "\u5fb7\u56fd\u5916\u4ea4\u90e8", "shape": "dot", "title": "\u5b9e\u4f53: \u5fb7\u56fd\u5916\u4ea4\u90e8\u003cbr\u003e\u5ea6\u6570: 4", "value": 12}, {"color": "#97c2fc", "font": {"color": "white"}, "id": "\u4e50\u7389\u6210", "label": "\u4e50\u7389\u6210", "shape": "dot", "title": "\u5b9e\u4f53: \u4e50\u7389\u6210\u003cbr\u003e\u5ea6\u6570: 4", "value": 12}, {"color": "#97c2fc", "font": {"color": "white"}, "id": "\u5fb7\u4e2d", "label": "\u5fb7\u4e2d", "shape": "dot", "title": "\u5b9e\u4f53: \u5fb7\u4e2d\u003cbr\u003e\u5ea6\u6570: 2", "value": 6}, {"color": "#97c2fc", "font": {"color": "white"}, "id": "\u8054\u5408\u56fd", "label": "\u8054\u5408\u56fd", "shape": "dot", "title": "\u5b9e\u4f53: \u8054\u5408\u56fd\u003cbr\u003e\u5ea6\u6570: 1", "value": 3}]);
                  edges = new vis.DataSet([{"color": "#87CEEB", "from": "\u5916\u4ea4\u90e8", "title": "\u003cb\u003e\u5173\u7cfb: \u5916\u4ea4\u90e8 - \u5b89\u5357\u003c/b\u003e\u003cbr\u003e\u003cb\u003e\u5171\u73b0\u6b21\u6570: 2\u003c/b\u003e\u003cbr\u003e\u003chr\u003e\u003cb\u003e\u6765\u6e90\u53e5\u5b50\u793a\u4f8b:\u003c/b\u003e\u003cbr\u003e  - \u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd 2020\u5e745\u670814\u65e5\uff0c\u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd\uff0c\u5c31\u6297\u75ab\u5408\u4f5c\u4ee5\u53ca\u4e2d\u5fb7\u3001\u4e2d\u6b27\u5173\u7cfb\u7b49\u4ea4\u6362\u610f\u89c1\u3002\u003cbr\u003e", "to": "\u5b89\u5357", "value": 2}, {"color": "#87CEEB", "from": "\u5916\u4ea4\u90e8", "title": "\u003cb\u003e\u5173\u7cfb: \u5916\u4ea4\u90e8 - \u4e2d\u5fb7\u003c/b\u003e\u003cbr\u003e\u003cb\u003e\u5171\u73b0\u6b21\u6570: 2\u003c/b\u003e\u003cbr\u003e\u003chr\u003e\u003cb\u003e\u6765\u6e90\u53e5\u5b50\u793a\u4f8b:\u003c/b\u003e\u003cbr\u003e  - \u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd 2020\u5e745\u670814\u65e5\uff0c\u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd\uff0c\u5c31\u6297\u75ab\u5408\u4f5c\u4ee5\u53ca\u4e2d\u5fb7\u3001\u4e2d\u6b27\u5173\u7cfb\u7b49\u4ea4\u6362\u610f\u89c1\u3002\u003cbr\u003e", "to": "\u4e2d\u5fb7", "value": 2}, {"color": "#87CEEB", "from": "\u5916\u4ea4\u90e8", "title": "\u003cb\u003e\u5173\u7cfb: \u5916\u4ea4\u90e8 - \u5fb7\u56fd\u5916\u4ea4\u90e8\u003c/b\u003e\u003cbr\u003e\u003cb\u003e\u5171\u73b0\u6b21\u6570: 2\u003c/b\u003e\u003cbr\u003e\u003chr\u003e\u003cb\u003e\u6765\u6e90\u53e5\u5b50\u793a\u4f8b:\u003c/b\u003e\u003cbr\u003e  - \u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd 2020\u5e745\u670814\u65e5\uff0c\u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd\uff0c\u5c31\u6297\u75ab\u5408\u4f5c\u4ee5\u53ca\u4e2d\u5fb7\u3001\u4e2d\u6b27\u5173\u7cfb\u7b49\u4ea4\u6362\u610f\u89c1\u3002\u003cbr\u003e", "to": "\u5fb7\u56fd\u5916\u4ea4\u90e8", "value": 2}, {"color": "#87CEEB", "from": "\u5916\u4ea4\u90e8", "title": "\u003cb\u003e\u5173\u7cfb: \u5916\u4ea4\u90e8 - \u4e50\u7389\u6210\u003c/b\u003e\u003cbr\u003e\u003cb\u003e\u5171\u73b0\u6b21\u6570: 2\u003c/b\u003e\u003cbr\u003e\u003chr\u003e\u003cb\u003e\u6765\u6e90\u53e5\u5b50\u793a\u4f8b:\u003c/b\u003e\u003cbr\u003e  - \u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd 2020\u5e745\u670814\u65e5\uff0c\u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd\uff0c\u5c31\u6297\u75ab\u5408\u4f5c\u4ee5\u53ca\u4e2d\u5fb7\u3001\u4e2d\u6b27\u5173\u7cfb\u7b49\u4ea4\u6362\u610f\u89c1\u3002\u003cbr\u003e", "to": "\u4e50\u7389\u6210", "value": 2}, {"color": "#87CEEB", "from": "\u5b89\u5357", "title": "\u003cb\u003e\u5173\u7cfb: \u5b89\u5357 - \u4e2d\u5fb7\u003c/b\u003e\u003cbr\u003e\u003cb\u003e\u5171\u73b0\u6b21\u6570: 2\u003c/b\u003e\u003cbr\u003e\u003chr\u003e\u003cb\u003e\u6765\u6e90\u53e5\u5b50\u793a\u4f8b:\u003c/b\u003e\u003cbr\u003e  - \u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd 2020\u5e745\u670814\u65e5\uff0c\u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd\uff0c\u5c31\u6297\u75ab\u5408\u4f5c\u4ee5\u53ca\u4e2d\u5fb7\u3001\u4e2d\u6b27\u5173\u7cfb\u7b49\u4ea4\u6362\u610f\u89c1\u3002\u003cbr\u003e", "to": "\u4e2d\u5fb7", "value": 2}, {"color": "#87CEEB", "from": "\u5b89\u5357", "title": "\u003cb\u003e\u5173\u7cfb: \u5b89\u5357 - \u5fb7\u56fd\u5916\u4ea4\u90e8\u003c/b\u003e\u003cbr\u003e\u003cb\u003e\u5171\u73b0\u6b21\u6570: 2\u003c/b\u003e\u003cbr\u003e\u003chr\u003e\u003cb\u003e\u6765\u6e90\u53e5\u5b50\u793a\u4f8b:\u003c/b\u003e\u003cbr\u003e  - \u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd 2020\u5e745\u670814\u65e5\uff0c\u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd\uff0c\u5c31\u6297\u75ab\u5408\u4f5c\u4ee5\u53ca\u4e2d\u5fb7\u3001\u4e2d\u6b27\u5173\u7cfb\u7b49\u4ea4\u6362\u610f\u89c1\u3002\u003cbr\u003e", "to": "\u5fb7\u56fd\u5916\u4ea4\u90e8", "value": 2}, {"color": "#87CEEB", "from": "\u5b89\u5357", "title": "\u003cb\u003e\u5173\u7cfb: \u5b89\u5357 - \u4e50\u7389\u6210\u003c/b\u003e\u003cbr\u003e\u003cb\u003e\u5171\u73b0\u6b21\u6570: 2\u003c/b\u003e\u003cbr\u003e\u003chr\u003e\u003cb\u003e\u6765\u6e90\u53e5\u5b50\u793a\u4f8b:\u003c/b\u003e\u003cbr\u003e  - \u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd 2020\u5e745\u670814\u65e5\uff0c\u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd\uff0c\u5c31\u6297\u75ab\u5408\u4f5c\u4ee5\u53ca\u4e2d\u5fb7\u3001\u4e2d\u6b27\u5173\u7cfb\u7b49\u4ea4\u6362\u610f\u89c1\u3002\u003cbr\u003e", "to": "\u4e50\u7389\u6210", "value": 2}, {"color": "#87CEEB", "from": "\u5b89\u5357", "title": "\u003cb\u003e\u5173\u7cfb: \u5b89\u5357 - \u5fb7\u4e2d\u003c/b\u003e\u003cbr\u003e\u003cb\u003e\u5171\u73b0\u6b21\u6570: 2\u003c/b\u003e\u003cbr\u003e\u003chr\u003e\u003cb\u003e\u6765\u6e90\u53e5\u5b50\u793a\u4f8b:\u003c/b\u003e\u003cbr\u003e  - \u5b89\u5357\u8868\u793a\uff0c\u5fb7\u4e2d\u5728\u5171\u540c\u6297\u51fb\u75ab\u60c5\u8fc7\u7a0b\u4e2d\u76f8\u4e92\u652f\u6301\u5e2e\u52a9\uff0c\u5f00\u5c55\u4e86\u5145\u6ee1\u4e92\u4fe1\u7684\u5408\u4f5c\u3002\u003cbr\u003e", "to": "\u5fb7\u4e2d", "value": 2}, {"color": "#87CEEB", "from": "\u4e2d\u5fb7", "title": "\u003cb\u003e\u5173\u7cfb: \u4e2d\u5fb7 - \u5fb7\u56fd\u5916\u4ea4\u90e8\u003c/b\u003e\u003cbr\u003e\u003cb\u003e\u5171\u73b0\u6b21\u6570: 2\u003c/b\u003e\u003cbr\u003e\u003chr\u003e\u003cb\u003e\u6765\u6e90\u53e5\u5b50\u793a\u4f8b:\u003c/b\u003e\u003cbr\u003e  - \u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd 2020\u5e745\u670814\u65e5\uff0c\u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd\uff0c\u5c31\u6297\u75ab\u5408\u4f5c\u4ee5\u53ca\u4e2d\u5fb7\u3001\u4e2d\u6b27\u5173\u7cfb\u7b49\u4ea4\u6362\u610f\u89c1\u3002\u003cbr\u003e", "to": "\u5fb7\u56fd\u5916\u4ea4\u90e8", "value": 2}, {"color": "#87CEEB", "from": "\u4e2d\u5fb7", "title": "\u003cb\u003e\u5173\u7cfb: \u4e2d\u5fb7 - \u4e50\u7389\u6210\u003c/b\u003e\u003cbr\u003e\u003cb\u003e\u5171\u73b0\u6b21\u6570: 2\u003c/b\u003e\u003cbr\u003e\u003chr\u003e\u003cb\u003e\u6765\u6e90\u53e5\u5b50\u793a\u4f8b:\u003c/b\u003e\u003cbr\u003e  - \u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd 2020\u5e745\u670814\u65e5\uff0c\u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd\uff0c\u5c31\u6297\u75ab\u5408\u4f5c\u4ee5\u53ca\u4e2d\u5fb7\u3001\u4e2d\u6b27\u5173\u7cfb\u7b49\u4ea4\u6362\u610f\u89c1\u3002\u003cbr\u003e", "to": "\u4e50\u7389\u6210", "value": 2}, {"color": "#87CEEB", "from": "\u5fb7\u56fd\u5916\u4ea4\u90e8", "title": "\u003cb\u003e\u5173\u7cfb: \u5fb7\u56fd\u5916\u4ea4\u90e8 - \u4e50\u7389\u6210\u003c/b\u003e\u003cbr\u003e\u003cb\u003e\u5171\u73b0\u6b21\u6570: 2\u003c/b\u003e\u003cbr\u003e\u003chr\u003e\u003cb\u003e\u6765\u6e90\u53e5\u5b50\u793a\u4f8b:\u003c/b\u003e\u003cbr\u003e  - \u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd 2020\u5e745\u670814\u65e5\uff0c\u5916\u4ea4\u90e8\u526f\u90e8\u957f\u4e50\u7389\u6210\u540c\u5fb7\u56fd\u5916\u4ea4\u90e8\u56fd\u52a1\u90e8\u957f\u5b89\u5357\u901a\u7535\u8bdd\uff0c\u5c31\u6297\u75ab\u5408\u4f5c\u4ee5\u53ca\u4e2d\u5fb7\u3001\u4e2d\u6b27\u5173\u7cfb\u7b49\u4ea4\u6362\u610f\u89c1\u3002\u003cbr\u003e", "to": "\u4e50\u7389\u6210", "value": 2}, {"color": "#87CEEB", "from": "\u5fb7\u4e2d", "title": "\u003cb\u003e\u5173\u7cfb: \u5fb7\u4e2d - \u8054\u5408\u56fd\u003c/b\u003e\u003cbr\u003e\u003cb\u003e\u5171\u73b0\u6b21\u6570: 2\u003c/b\u003e\u003cbr\u003e\u003chr\u003e\u003cb\u003e\u6765\u6e90\u53e5\u5b50\u793a\u4f8b:\u003c/b\u003e\u003cbr\u003e  - \u5728\u7ef4\u62a4\u548c\u53d1\u6325\u8054\u5408\u56fd\u548c\u4e16\u536b\u7ec4\u7ec7\u7b49\u591a\u8fb9\u673a\u6784\u5173\u952e\u4f5c\u7528\u65b9\u9762\uff0c\u5fb7\u4e2d\u6709\u76f8\u4f3c\u7acb\u573a\u3002\u003cbr\u003e", "to": "\u8054\u5408\u56fd", "value": 2}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {
    "configure": {
        "enabled": true,
        "filter": [
            "physics"
        ]
    },
    "edges": {
        "color": {
            "inherit": true
        },
        "smooth": {
            "enabled": true,
            "type": "dynamic"
        }
    },
    "interaction": {
        "dragNodes": true,
        "hideEdgesOnDrag": false,
        "hideNodesOnDrag": false
    },
    "physics": {
        "enabled": true,
        "repulsion": {
            "centralGravity": 0.2,
            "damping": 0.09,
            "nodeDistance": 150,
            "springConstant": 0.05,
            "springLength": 200
        },
        "solver": "repulsion",
        "stabilization": {
            "enabled": true,
            "fit": true,
            "iterations": 1000,
            "onlyDynamicEdges": false,
            "updateInterval": 50
        }
    }
};

                  


                  
                  // if this network requires displaying the configure window,
                  // put it in its div
                  options.configure["container"] = document.getElementById("config");
                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>