#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import re
import jieba
import spacy
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
from collections import defaultdict
import networkx as nx
from pyvis.network import Network
import json
import os

# --- 全局配置 ---
DATA_FILE = "作业数据.csv"
OUTPUT_DIR = "analysis_results"
N_CLUSTERS = 5
TOP_N_KEYWORDS = 15

# 加载spaCy中文模型
try:
    NLP = spacy.load("zh_core_web_sm")
except OSError:
    print("错误: spaCy中文模型 'zh_core_web_sm' 未找到。")
    print("请运行: python -m spacy download zh_core_web_sm")
    exit()

def load_and_clean_data(filename):
    """
    加载并清理数据
    
    Args:
        filename (str): 数据文件名
    
    Returns:
        pd.DataFrame: 清理后的数据框
    """
    print(f"1.1. 正在加载数据: {filename}")
    try:
        try:
            df = pd.read_csv(filename, encoding='utf-8')
        except UnicodeDecodeError:
            df = pd.read_csv(filename, encoding='gbk')
        df['text'] = df['title'].fillna('') + ' ' + df['content'].fillna('')
        df['text'] = df['text'].apply(lambda x: re.sub(r'\s+', ' ', x).strip())
        df.dropna(subset=['text'], inplace=True)
        print(f"数据加载成功，共 {len(df)} 条新闻。")
        return df
    except FileNotFoundError:
        print(f"错误: 数据文件 {filename} 未找到。")
        return None
    except Exception as e:
        print(f"加载数据时发生未知错误: {e}")
        return None

def chinese_word_segmentation(text):
    """
    中文分词
    
    Args:
        text (str): 输入文本
    
    Returns:
        str: 分词后的文本
    """
    return " ".join(jieba.cut(text))

def classify_news_by_theme(df, n_clusters):
    """
    使用K-Means对新闻进行主题聚类
    
    Args:
        df (pd.DataFrame): 新闻数据框
        n_clusters (int): 聚类数量
    
    Returns:
        pd.DataFrame: 添加了主题标签的数据框
    """
    print(f"\n2.1. 正在对 {len(df)} 条新闻进行主题聚类 (K={n_clusters})...")
    df['segmented_text'] = df['text'].apply(chinese_word_segmentation)
    vectorizer = TfidfVectorizer(max_df=0.95, min_df=2, stop_words=['的', '在', '和', '是', '了', '也', '中', '国', '德国'])
    tfidf_matrix = vectorizer.fit_transform(df['segmented_text'])
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
    kmeans.fit(tfidf_matrix)
    df['theme'] = kmeans.labels_
    print("2.2. 主题聚类完成。正在提取各主题关键词...")
    feature_names = vectorizer.get_feature_names_out()
    top_keywords = {}
    for i in range(n_clusters):
        order_centroids = kmeans.cluster_centers_.argsort()[:, ::-1]
        terms = [feature_names[ind] for ind in order_centroids[i, :TOP_N_KEYWORDS]]
        top_keywords[f"主题 {i}"] = terms
        print(f"主题 {i}: {', '.join(terms)}")
    with open(os.path.join(OUTPUT_DIR, 'top_keywords_per_theme.json'), 'w', encoding='utf-8') as f:
        json.dump(top_keywords, f, ensure_ascii=False, indent=4)
    return df

def normalize_entity_text(text):
    """
    实体文本归一化处理
    
    Args:
        text (str): 原始实体文本
    
    Returns:
        str: 归一化后的实体文本
    """
    text = re.sub(r'(说|表示|指出|强调|认为|主席|总理|外长|部长)$', '', text)
    if "习近平" in text: return "习近平"
    if "李克强" in text: return "李克强"
    if "朔尔茨" in text: return "朔尔茨"
    if "贝尔伯克" in text: return "贝尔伯克"
    return text

def extract_entities_and_relations(text):
    """
    使用spaCy从单篇新闻中提取实体和带上下文的关系。
    关系是一个字典列表，每个字典包含实体对和来源句子。
    
    Args:
        text (str): 新闻文本
    
    Returns:
        tuple: (entities, relations) 实体字典和关系列表
    """
    doc = NLP(text)
    entities = defaultdict(set)
    relations = []
    
    entity_map = {}
    for ent in doc.ents:
        if ent.label_ in ['PERSON', 'GPE', 'ORG'] and len(ent.text) > 1:
            original_text = ent.text.strip()
            normalized_text = normalize_entity_text(original_text)
            if len(normalized_text) > 1:
                entity_map[original_text] = normalized_text
                entities[ent.label_].add(normalized_text)

    for sent in doc.sents:
        sent_entities = []
        for ent in sent.ents:
            original_text = ent.text.strip()
            if original_text in entity_map:
                sent_entities.append(entity_map[original_text])
        
        unique_sent_entities = list(set(sent_entities))
        if len(unique_sent_entities) >= 2:
            for i in range(len(unique_sent_entities)):
                for j in range(i + 1, len(unique_sent_entities)):
                    # 存储关系字典，包含实体对和句子上下文
                    relations.append({
                        "edge": tuple(sorted((unique_sent_entities[i], unique_sent_entities[j]))),
                        "sentence": sent.text.strip()
                    })
                    
    return entities, relations

def build_and_analyze_graph(relations, theme_name):
    """
    根据关系列表构建、分析并可视化知识图谱，边上带有悬停提示。
    
    Args:
        relations (list): 关系列表
        theme_name (str): 主题名称
    
    Returns:
        dict: 图分析统计结果
    """
    print(f"\n4.1. 正在为 '{theme_name}' 构建知识图谱...")
    
    # 聚合关系，统计权重和来源句子
    edge_data = defaultdict(lambda: {'weight': 0, 'sentences': []})
    for rel in relations:
        edge = rel['edge']
        edge_data[edge]['weight'] += 1
        # 只添加不重复的句子以避免提示信息过长
        if rel['sentence'] not in edge_data[edge]['sentences']:
            edge_data[edge]['sentences'].append(rel['sentence'])

    G = nx.Graph()
    for edge, data in edge_data.items():
        u, v = edge
        if u != v:
            G.add_edge(u, v, weight=data['weight'], sentences=data['sentences'])
            
    if not G.nodes:
        print(f"'{theme_name}' 没有发现足够的关系来构建图。")
        return None

    print(f"图构建完成: {G.number_of_nodes()} 个节点, {G.number_of_edges()} 条边。")
    
    degrees = G.degree()
    node_degrees = {node: val for node, val in degrees}
    top_10_nodes = sorted(node_degrees.items(), key=lambda x: x[1], reverse=True)[:10]

    print("度数中心性排名前10的节点:")
    for node, degree in top_10_nodes:
        print(f"  - {node}: {degree}")
        
    net = Network(height="800px", width="100%", bgcolor="#222222", font_color="white", notebook=False, directed=False)
    
    for node, degree in node_degrees.items():
        net.add_node(node, label=node, value=degree * 3, title=f"实体: {node}<br>度数: {degree}")

    for u, v, data in G.edges(data=True):
        weight = data['weight']
        sentences = data['sentences']
        
        # 创建悬停提示文本 (Tooltip)
        hover_title = f"<b>关系: {u} - {v}</b><br>"
        hover_title += f"<b>共现次数: {weight}</b><br><hr>"
        hover_title += "<b>来源句子示例:</b><br>"
        # 最多显示5条来源句子
        for i, sent in enumerate(sentences[:5]):
            hover_title += f"  - {sent}<br>"
        if len(sentences) > 5:
            hover_title += f"<i>(...等共 {len(sentences)} 条)</i>"
            
        net.add_edge(u, v, value=weight, title=hover_title, color='#87CEEB')
        
    net.repulsion(node_distance=150, spring_length=200)
    net.show_buttons(filter_=['physics'])
    
    output_filename = os.path.join(OUTPUT_DIR, f"knowledge_graph_{theme_name.replace(' ', '_')}.html")
    net.save_graph(output_filename)
    print(f"已保存可交互图谱: {output_filename}")
    
    return {
        "nodes_count": G.number_of_nodes(),
        "edges_count": G.number_of_edges(),
        "top_10_central_nodes": top_10_nodes
    }

def main():
    """
    主执行函数，完成整个知识图谱构建与分析流程
    """
    print("=== 新闻知识图谱构建与分析 ===")
    
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        
    df = load_and_clean_data(DATA_FILE)
    if df is None: return

    df_themed = classify_news_by_theme(df, n_clusters=N_CLUSTERS)
    
    thematic_reports = {}
    
    for theme_id in range(N_CLUSTERS):
        theme_name = f"主题 {theme_id}"
        print(f"\n{'='*20} 正在分析 {theme_name} {'='*20}")
        theme_df = df_themed[df_themed['theme'] == theme_id]
        
        all_theme_entities = defaultdict(set)
        all_theme_relations = []
        for text in theme_df['text']:
            entities, relations = extract_entities_and_relations(text)
            for label, ents in entities.items():
                all_theme_entities[label].update(ents)
            all_theme_relations.extend(relations)
        
        print(f"在 '{theme_name}' 中共识别出 {sum(len(s) for s in all_theme_entities.values())} 个独立实体。")
        print(f"共抽取到 {len(all_theme_relations)} 条关系记录。")
        
        graph_stats = build_and_analyze_graph(all_theme_relations, theme_name)
        if graph_stats:
            thematic_reports[theme_name] = {
                "news_count": len(theme_df),
                "entity_counts": {label: len(ents) for label, ents in all_theme_entities.items()},
                "relation_record_count": len(all_theme_relations),
                "graph_analysis": graph_stats
            }

    
    report_filename = os.path.join(OUTPUT_DIR, 'thematic_analysis_report.json')
    with open(report_filename, 'w', encoding='utf-8') as f:
        json.dump(thematic_reports, f, ensure_ascii=False, indent=4)
    print(f"\n已保存完整分析报告: {report_filename}")

    print("\n=== 分析流程全部完成 ===")

# 执行主程序
if __name__ == "__main__":
    main()

# 示例：单独加载数据
# df = load_and_clean_data(DATA_FILE)
# print(f"数据形状: {df.shape}")
# print(df.head())

# 示例：单独进行主题分类
# df_themed = classify_news_by_theme(df, n_clusters=N_CLUSTERS)
# print(df_themed['theme'].value_counts())

# 示例：单独测试实体识别
# sample_text = "习近平主席与德国总理朔尔茨在北京举行会谈。"
# entities, relations = extract_entities_and_relations(sample_text)
# print(f"实体: {entities}")
# print(f"关系: {relations}")